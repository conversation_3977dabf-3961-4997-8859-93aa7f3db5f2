.products {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 40px;
	width: 90%;
	max-width: 1200px;
	margin: 0 auto;
	background: #fff;
	font-family: 'Raleway', sans-serif;
	padding: 40px 0;
}


.product-card {
  position: relative;
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 0;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  height: 100%;
}

.product-card:hover {
  box-shadow: none;
}

.product-card__image {
  width: 100%;
  height: 330px;
  object-fit: cover;
  display: block;
}

.product-card__brand {
  margin: 24px 0 0 0;
  font-size: 18px;
  font-weight: 300;
  color: #1d1f22;
  font-family: 'Raleway', sans-serif;
  line-height: 1.6;
}

.product-card__description {
  font-weight: normal;
}

.product-card__price {
  font-weight: bold;
}

.product-card__btn-wishlist {
  position: absolute;
  top: 10px;
  right: 10px;
  border-radius: 50%;
  height: 40px;
  width: 40px;
  border: none;
  background-color: white;
  padding: 12px 10px 10px;
  box-shadow: 2px 2px 5px 0px rgba(0, 64, 128, 0.1);
}

.product-card__btn-wishlist svg {
  fill: lightgrey;
}

.product-link{
  text-decoration: none;
  color: #1d1f22;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0 16px;
}

.product-link .product-name {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 300;
  color: #1d1f22;
  font-family: 'Raleway', sans-serif;
  line-height: 1.6;
}

.header {
  grid-column: 1 / -1;
  margin-bottom: 103px;
  color: #1d1f22;
  font-family: 'Raleway', sans-serif;
}

.header h1 {
  font-size: 42px;
  font-weight: 400;
  margin: 0;
  text-transform: capitalize;
}

.price {
  margin: 8px 0 24px 0;
  font-size: 18px;
  font-weight: 500;
  color: #1d1f22;
  font-family: 'Raleway', sans-serif;
}

/* Add to Cart Button */
.add-to-cart {
  display: block;
  width: 100%;
  padding: 0.75rem;
  font-size: 1rem;
  font-weight: bold;
  color: white;
  background-color: turquoise;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s;
  margin: 0;
  position: relative;
  top: -10px;
}

.add-to-cart:hover {
  background-color: rgb(25, 139, 128);
}

/* Quick Shop Button */
.quick-shop-btn {
  position: absolute;
  bottom: 72px;
  right: 31px;
  width: 52px;
  height: 52px;
  border-radius: 50%;
  border: none;
  background-color: #5ECE7B;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 11px rgba(29, 31, 34, 0.1);
  transition: all 0.3s ease;
  opacity: 0;
  visibility: hidden;
  transform: scale(0.8);
  z-index: 10;
}

.quick-shop-btn:hover {
  background-color: #4CAF50;
  transform: scale(1.1);
  box-shadow: 0 6px 15px rgba(29, 31, 34, 0.2);
}

.product-card:hover .quick-shop-btn {
  opacity: 1;
  visibility: visible;
  transform: scale(1);
}

.quick-shop-btn svg {
  width: 20px;
  height: 20px;
}

/* Out of Stock Overlay */
.out-of-stock-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.73);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 5;
}

.out-of-stock-text {
  color: #8D8F9A;
  font-size: 24px;
  font-weight: 400;
  font-family: 'Raleway', sans-serif;
  text-transform: uppercase;
}