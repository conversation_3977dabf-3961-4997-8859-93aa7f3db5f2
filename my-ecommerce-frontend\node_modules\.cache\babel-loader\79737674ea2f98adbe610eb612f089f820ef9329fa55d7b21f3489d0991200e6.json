{"ast": null, "code": "var _jsxFileName = \"D:\\\\SCAAND_PRO\\\\my-ecommerce-frontend\\\\src\\\\App.js\";\nimport Navbar from './components/Navbar';\nimport { BrowserRouter as Router, Route, Routes } from 'react-router-dom';\nimport ProductList from './components/ProductList';\nimport CartPopup from './components/CartPopup';\nimport ProductDetails from './components/ProductDetails';\nimport 'typeface-raleway';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(ProductList, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 34\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/product/:id\",\n          element: /*#__PURE__*/_jsxDEV(ProductDetails, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/category/clothes\",\n          element: /*#__PURE__*/_jsxDEV(ProductList, {\n            category_id: 14,\n            category: \"clothes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 50\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/category/tech\",\n          element: /*#__PURE__*/_jsxDEV(ProductList, {\n            category_id: 15,\n            category: \"tech\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cart\",\n          element: /*#__PURE__*/_jsxDEV(CartPopup, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 38\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 5\n    }, this)\n  }, void 0, false);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Route", "Routes", "ProductList", "CartPopup", "ProductDetails", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "category_id", "category", "_c", "$RefreshReg$"], "sources": ["D:/SCAAND_PRO/my-ecommerce-frontend/src/App.js"], "sourcesContent": ["\nimport Navbar from './components/Navbar';\nimport { BrowserRouter as Router, Route, Routes } from 'react-router-dom';\nimport ProductList from './components/ProductList';\nimport CartPopup from './components/CartPopup';\nimport ProductDetails from './components/ProductDetails';\nimport 'typeface-raleway';\n\n\nfunction App() {\n  return (\n    <>\n    <Router>\n    <Navbar></Navbar>\n      <Routes>\n        <Route path=\"/\" element={<ProductList />} />\n        <Route path=\"/product/:id\" element={<ProductDetails />} />\n        <Route path=\"/category/clothes\" element={<ProductList category_id={14} category=\"clothes\" />} />\n        <Route path=\"/category/tech\" element={<ProductList category_id={15} category=\"tech\" />} />\n        <Route path=\"/cart\" element={<CartPopup />} />\n\n      </Routes>\n    </Router>\n    </>\n  );\n}\n\nexport default App;\n"], "mappings": ";AACA,OAAOA,MAAM,MAAM,qBAAqB;AACxC,SAASC,aAAa,IAAIC,MAAM,EAAEC,KAAK,EAAEC,MAAM,QAAQ,kBAAkB;AACzE,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAG1B,SAASC,GAAGA,CAAA,EAAG;EACb,oBACEH,OAAA,CAAAE,SAAA;IAAAE,QAAA,eACAJ,OAAA,CAACP,MAAM;MAAAW,QAAA,gBACPJ,OAAA,CAACT,MAAM;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eACfR,OAAA,CAACL,MAAM;QAAAS,QAAA,gBACLJ,OAAA,CAACN,KAAK;UAACe,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEV,OAAA,CAACJ,WAAW;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5CR,OAAA,CAACN,KAAK;UAACe,IAAI,EAAC,cAAc;UAACC,OAAO,eAAEV,OAAA,CAACF,cAAc;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DR,OAAA,CAACN,KAAK;UAACe,IAAI,EAAC,mBAAmB;UAACC,OAAO,eAAEV,OAAA,CAACJ,WAAW;YAACe,WAAW,EAAE,EAAG;YAACC,QAAQ,EAAC;UAAS;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChGR,OAAA,CAACN,KAAK;UAACe,IAAI,EAAC,gBAAgB;UAACC,OAAO,eAAEV,OAAA,CAACJ,WAAW;YAACe,WAAW,EAAE,EAAG;YAACC,QAAQ,EAAC;UAAM;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1FR,OAAA,CAACN,KAAK;UAACe,IAAI,EAAC,OAAO;UAACC,OAAO,eAAEV,OAAA,CAACH,SAAS;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAExC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC,gBACP,CAAC;AAEP;AAACK,EAAA,GAhBQV,GAAG;AAkBZ,eAAeA,GAAG;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}