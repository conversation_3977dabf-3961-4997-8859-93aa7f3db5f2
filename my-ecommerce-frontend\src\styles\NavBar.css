/* src/components/Navbar.css */

.navbar {
    position: sticky;
    top: 0;
    width: 100%;
    background-color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 101px;
    z-index: 1000;
    height: 80px;
    font-family: 'Raleway', sans-serif;
    box-sizing: border-box;
  }
  
  .navbar-left, .navbar-right {
    display: flex;
    gap: 2rem;
    align-items: center;
  }

  .navbar-left a {
    color: #1d1f22;
    text-decoration: none;
    height: auto;
    font-size: 16px;
    font-weight: 400;
    text-transform: uppercase;
    padding: 28px 0;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
  }

  .navbar-left a.active {
    color: #5ECE7B;
    border-bottom: 2px solid #5ECE7B;
  }

  .navbar-left a:hover {
    color: #5ECE7B;
  }

  
  .nav-link {
    color: white;
    text-decoration: none;
    font-size: 1rem;
    transition: color 0.3s ease;
  }
  
  .nav-link:hover {
    color: #ddd;
  }

  .cart-icon-container {
    position: relative;
    cursor: pointer;
  }
  
  .cart-icon {
    font-size: 1.5rem;
  }
  
  .cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #1d1f22;
    color: white;
    font-size: 11px;
    font-weight: 700;
    padding: 0;
    border-radius: 50%;
    text-align: center;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Roboto', sans-serif;
  }
  
  