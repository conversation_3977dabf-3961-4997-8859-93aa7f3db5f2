{"ast": null, "code": "var _jsxFileName = \"D:\\\\SCAAND_PRO\\\\my-ecommerce-frontend\\\\src\\\\components\\\\Navbar.js\",\n  _s = $RefreshSig$();\n// src/components/Navbar.js\nimport { useState } from 'react';\nimport CartPopup from './CartPopup';\nimport { useQuery } from '@apollo/client';\nimport { GET_CART_QUERY } from '../graphql/queries';\nimport { NavLink } from 'react-router-dom';\nimport logo from '../assets/img/Group.svg';\nimport cart from '../assets/img/Vector.svg';\nimport '../styles/NavBar.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Navbar() {\n  _s();\n  var _data$cart;\n  const [isCartOpen, setIsCartOpen] = useState(false);\n  const {\n    data\n  } = useQuery(GET_CART_QUERY);\n  const cartCount = (data === null || data === void 0 ? void 0 : (_data$cart = data.cart) === null || _data$cart === void 0 ? void 0 : _data$cart.reduce((total, item) => total + item.quantity, 0)) || 0;\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"navbar\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-left\",\n      children: [/*#__PURE__*/_jsxDEV(NavLink, {\n        to: \"/\",\n        className: ({\n          isActive\n        }) => isActive ? 'active' : '',\n        \"data-testid\": ({\n          isActive\n        }) => isActive ? 'active-category-link' : 'category-link',\n        children: \"All\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(NavLink, {\n        to: \"/category/clothes\",\n        className: ({\n          isActive\n        }) => isActive ? 'active' : '',\n        \"data-testid\": ({\n          isActive\n        }) => isActive ? 'active-category-link' : 'category-link',\n        children: \"Clothes\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(NavLink, {\n        to: \"/category/tech\",\n        className: ({\n          isActive\n        }) => isActive ? 'active' : '',\n        \"data-testid\": ({\n          isActive\n        }) => isActive ? 'active-category-link' : 'category-link',\n        children: \"Tech\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n      src: logo,\n      width: 30,\n      height: 30,\n      alt: \"Logo\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-right\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cart-icon-container\",\n        \"data-testid\": \"cart-btn\",\n        onClick: () => setIsCartOpen(!isCartOpen),\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: cart,\n          width: 20,\n          height: 20,\n          alt: \"Cart\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), cartCount === 0 ? null : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"cart-count\",\n          children: cartCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CartPopup, {\n      isOpen: isCartOpen,\n      closePopup: () => setIsCartOpen(false),\n      cartItems: (data === null || data === void 0 ? void 0 : data.cart) || []\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n}\n_s(Navbar, \"t3fC1De54CuZD/78U2oqdPhtT0A=\", false, function () {\n  return [useQuery];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["useState", "CartPopup", "useQuery", "GET_CART_QUERY", "NavLink", "logo", "cart", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "_s", "_data$cart", "isCartOpen", "setIsCartOpen", "data", "cartCount", "reduce", "total", "item", "quantity", "className", "children", "to", "isActive", "data-testid", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "width", "height", "alt", "onClick", "isOpen", "closePopup", "cartItems", "_c", "$RefreshReg$"], "sources": ["D:/SCAAND_PRO/my-ecommerce-frontend/src/components/Navbar.js"], "sourcesContent": ["// src/components/Navbar.js\r\nimport { useState } from 'react';\r\nimport CartPopup from './CartPopup';\r\nimport { useQuery } from '@apollo/client';\r\nimport { GET_CART_QUERY } from '../graphql/queries';\r\nimport { NavLink } from 'react-router-dom';\r\nimport logo from '../assets/img/Group.svg';\r\nimport cart from '../assets/img/Vector.svg';\r\nimport '../styles/NavBar.css';\r\n\r\nfunction Navbar() {\r\n  const [isCartOpen, setIsCartOpen] = useState(false);\r\n  const { data } = useQuery(GET_CART_QUERY);\r\n\r\n  const cartCount = data?.cart?.reduce((total, item) => total + item.quantity, 0) || 0;\r\n\r\n  return (\r\n    <nav className=\"navbar\">\r\n      <div className=\"navbar-left\">\r\n        <NavLink to=\"/\" className={({ isActive }) => (isActive ? 'active' : '')} data-testid={({ isActive }) => (isActive ? 'active-category-link' : 'category-link')}>All</NavLink>\r\n        <NavLink to=\"/category/clothes\" className={({ isActive }) => (isActive ? 'active' : '')} data-testid={({ isActive }) => (isActive ? 'active-category-link' : 'category-link')}>Clothes</NavLink>\r\n        <NavLink to=\"/category/tech\" className={({ isActive }) => (isActive ? 'active' : '')} data-testid={({ isActive }) => (isActive ? 'active-category-link' : 'category-link')}>Tech</NavLink>\r\n      </div>\r\n      <img src={logo} width={30} height={30} alt=\"Logo\"/>\r\n      <div className=\"navbar-right\">\r\n        <div className=\"cart-icon-container\" data-testid=\"cart-btn\" onClick={() => setIsCartOpen(!isCartOpen)}>\r\n          <img src={cart} width={20} height={20} alt=\"Cart\"/>\r\n          {cartCount === 0 ? null : (\r\n            <span className=\"cart-count\">{cartCount}</span>\r\n          )}\r\n        </div>\r\n      </div>\r\n      <CartPopup\r\n        isOpen={isCartOpen}\r\n        closePopup={() => setIsCartOpen(false)}\r\n        cartItems={data?.cart || []}\r\n      />\r\n    </nav>\r\n  );\r\n}\r\n\r\nexport default Navbar;\r\n"], "mappings": ";;AAAA;AACA,SAASA,QAAQ,QAAQ,OAAO;AAChC,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,OAAOC,IAAI,MAAM,yBAAyB;AAC1C,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,UAAA;EAChB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM;IAAEc;EAAK,CAAC,GAAGZ,QAAQ,CAACC,cAAc,CAAC;EAEzC,MAAMY,SAAS,GAAG,CAAAD,IAAI,aAAJA,IAAI,wBAAAH,UAAA,GAAJG,IAAI,CAAER,IAAI,cAAAK,UAAA,uBAAVA,UAAA,CAAYK,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAKD,KAAK,GAAGC,IAAI,CAACC,QAAQ,EAAE,CAAC,CAAC,KAAI,CAAC;EAEpF,oBACEX,OAAA;IAAKY,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBACrBb,OAAA;MAAKY,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1Bb,OAAA,CAACJ,OAAO;QAACkB,EAAE,EAAC,GAAG;QAACF,SAAS,EAAEA,CAAC;UAAEG;QAAS,CAAC,KAAMA,QAAQ,GAAG,QAAQ,GAAG,EAAI;QAAC,eAAaC,CAAC;UAAED;QAAS,CAAC,KAAMA,QAAQ,GAAG,sBAAsB,GAAG,eAAiB;QAAAF,QAAA,EAAC;MAAG;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eAC5KpB,OAAA,CAACJ,OAAO;QAACkB,EAAE,EAAC,mBAAmB;QAACF,SAAS,EAAEA,CAAC;UAAEG;QAAS,CAAC,KAAMA,QAAQ,GAAG,QAAQ,GAAG,EAAI;QAAC,eAAaC,CAAC;UAAED;QAAS,CAAC,KAAMA,QAAQ,GAAG,sBAAsB,GAAG,eAAiB;QAAAF,QAAA,EAAC;MAAO;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eAChMpB,OAAA,CAACJ,OAAO;QAACkB,EAAE,EAAC,gBAAgB;QAACF,SAAS,EAAEA,CAAC;UAAEG;QAAS,CAAC,KAAMA,QAAQ,GAAG,QAAQ,GAAG,EAAI;QAAC,eAAaC,CAAC;UAAED;QAAS,CAAC,KAAMA,QAAQ,GAAG,sBAAsB,GAAG,eAAiB;QAAAF,QAAA,EAAC;MAAI;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvL,CAAC,eACNpB,OAAA;MAAKqB,GAAG,EAAExB,IAAK;MAACyB,KAAK,EAAE,EAAG;MAACC,MAAM,EAAE,EAAG;MAACC,GAAG,EAAC;IAAM;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAAC,eACnDpB,OAAA;MAAKY,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3Bb,OAAA;QAAKY,SAAS,EAAC,qBAAqB;QAAC,eAAY,UAAU;QAACa,OAAO,EAAEA,CAAA,KAAMpB,aAAa,CAAC,CAACD,UAAU,CAAE;QAAAS,QAAA,gBACpGb,OAAA;UAAKqB,GAAG,EAAEvB,IAAK;UAACwB,KAAK,EAAE,EAAG;UAACC,MAAM,EAAE,EAAG;UAACC,GAAG,EAAC;QAAM;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC,EAClDb,SAAS,KAAK,CAAC,GAAG,IAAI,gBACrBP,OAAA;UAAMY,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAEN;QAAS;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC/C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNpB,OAAA,CAACP,SAAS;MACRiC,MAAM,EAAEtB,UAAW;MACnBuB,UAAU,EAAEA,CAAA,KAAMtB,aAAa,CAAC,KAAK,CAAE;MACvCuB,SAAS,EAAE,CAAAtB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAER,IAAI,KAAI;IAAG;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAAClB,EAAA,CA7BQD,MAAM;EAAA,QAEIP,QAAQ;AAAA;AAAAmC,EAAA,GAFlB5B,MAAM;AA+Bf,eAAeA,MAAM;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}