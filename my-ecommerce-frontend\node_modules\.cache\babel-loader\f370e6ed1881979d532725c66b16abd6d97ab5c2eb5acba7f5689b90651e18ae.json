{"ast": null, "code": "import { ApolloClient, InMemoryCache, from, createHttpLink } from '@apollo/client';\nimport { onError } from '@apollo/client/link/error';\n\n// Error handling link\nconst errorLink = onError(({\n  graphQLErrors,\n  networkError\n}) => {\n  if (graphQLErrors) {\n    graphQLErrors.forEach(({\n      message,\n      locations,\n      path\n    }) => console.error(`[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`));\n  }\n  if (networkError) {\n    console.error(`[Network error]: ${networkError}`);\n  }\n});\n\n// HTTP link\nconst graphqlEndpoint = process.env.REACT_APP_GRAPHQL_ENDPOINT || 'http://localhost:8000/graphql.php';\nconst httpLink = createHttpLink({\n  uri: graphqlEndpoint\n});\nconst client = new ApolloClient({\n  link: from([errorLink, httpLink]),\n  cache: new InMemoryCache({\n    typePolicies: {\n      Query: {\n        fields: {\n          cart: {\n            merge: false,\n            // Don't merge, replace the entire array\n            // Always fetch from network after mutations\n            read(existing) {\n              return existing;\n            }\n          }\n        }\n      }\n    }\n  }),\n  defaultOptions: {\n    watchQuery: {\n      fetchPolicy: 'network-only',\n      errorPolicy: 'all'\n    },\n    query: {\n      fetchPolicy: 'network-only',\n      errorPolicy: 'all'\n    },\n    mutate: {\n      errorPolicy: 'all'\n    }\n  }\n});\n\n// Clear cache on startup\nclient.clearStore();\nexport default client;", "map": {"version": 3, "names": ["ApolloClient", "InMemoryCache", "from", "createHttpLink", "onError", "errorLink", "graphQLErrors", "networkError", "for<PERSON>ach", "message", "locations", "path", "console", "error", "graphqlEndpoint", "process", "env", "REACT_APP_GRAPHQL_ENDPOINT", "httpLink", "uri", "client", "link", "cache", "typePolicies", "Query", "fields", "cart", "merge", "read", "existing", "defaultOptions", "watch<PERSON><PERSON>y", "fetchPolicy", "errorPolicy", "query", "mutate", "clearStore"], "sources": ["D:/SCAAND_PRO/my-ecommerce-frontend/src/apolloClient.js"], "sourcesContent": ["import { ApolloClient, InMemoryCache, from, createHttpLink } from '@apollo/client';\r\nimport { onError } from '@apollo/client/link/error';\r\n\r\n// Error handling link\r\nconst errorLink = onError(({ graphQLErrors, networkError }) => {\r\n  if (graphQLErrors) {\r\n    graphQLErrors.forEach(({ message, locations, path }) =>\r\n      console.error(\r\n        `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`\r\n      )\r\n    );\r\n  }\r\n  if (networkError) {\r\n    console.error(`[Network error]: ${networkError}`);\r\n  }\r\n});\r\n\r\n// HTTP link\r\nconst graphqlEndpoint = process.env.REACT_APP_GRAPHQL_ENDPOINT || 'http://localhost:8000/graphql.php';\r\n\r\nconst httpLink = createHttpLink({\r\n  uri: graphqlEndpoint,\r\n});\r\n\r\nconst client = new ApolloClient({\r\n  link: from([errorLink, httpLink]),\r\n  cache: new InMemoryCache({\r\n    typePolicies: {\r\n      Query: {\r\n        fields: {\r\n          cart: {\r\n            merge: false, // Don't merge, replace the entire array\r\n            // Always fetch from network after mutations\r\n            read(existing) {\r\n              return existing;\r\n            }\r\n          },\r\n        },\r\n      },\r\n    },\r\n  }),\r\n  defaultOptions: {\r\n    watchQuery: {\r\n      fetchPolicy: 'network-only',\r\n      errorPolicy: 'all',\r\n    },\r\n    query: {\r\n      fetchPolicy: 'network-only',\r\n      errorPolicy: 'all',\r\n    },\r\n    mutate: {\r\n      errorPolicy: 'all',\r\n    },\r\n  },\r\n});\r\n\r\n// Clear cache on startup\r\nclient.clearStore();\r\n\r\nexport default client;\r\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,aAAa,EAAEC,IAAI,EAAEC,cAAc,QAAQ,gBAAgB;AAClF,SAASC,OAAO,QAAQ,2BAA2B;;AAEnD;AACA,MAAMC,SAAS,GAAGD,OAAO,CAAC,CAAC;EAAEE,aAAa;EAAEC;AAAa,CAAC,KAAK;EAC7D,IAAID,aAAa,EAAE;IACjBA,aAAa,CAACE,OAAO,CAAC,CAAC;MAAEC,OAAO;MAAEC,SAAS;MAAEC;IAAK,CAAC,KACjDC,OAAO,CAACC,KAAK,CACX,6BAA6BJ,OAAO,eAAeC,SAAS,WAAWC,IAAI,EAC7E,CACF,CAAC;EACH;EACA,IAAIJ,YAAY,EAAE;IAChBK,OAAO,CAACC,KAAK,CAAC,oBAAoBN,YAAY,EAAE,CAAC;EACnD;AACF,CAAC,CAAC;;AAEF;AACA,MAAMO,eAAe,GAAGC,OAAO,CAACC,GAAG,CAACC,0BAA0B,IAAI,mCAAmC;AAErG,MAAMC,QAAQ,GAAGf,cAAc,CAAC;EAC9BgB,GAAG,EAAEL;AACP,CAAC,CAAC;AAEF,MAAMM,MAAM,GAAG,IAAIpB,YAAY,CAAC;EAC9BqB,IAAI,EAAEnB,IAAI,CAAC,CAACG,SAAS,EAAEa,QAAQ,CAAC,CAAC;EACjCI,KAAK,EAAE,IAAIrB,aAAa,CAAC;IACvBsB,YAAY,EAAE;MACZC,KAAK,EAAE;QACLC,MAAM,EAAE;UACNC,IAAI,EAAE;YACJC,KAAK,EAAE,KAAK;YAAE;YACd;YACAC,IAAIA,CAACC,QAAQ,EAAE;cACb,OAAOA,QAAQ;YACjB;UACF;QACF;MACF;IACF;EACF,CAAC,CAAC;EACFC,cAAc,EAAE;IACdC,UAAU,EAAE;MACVC,WAAW,EAAE,cAAc;MAC3BC,WAAW,EAAE;IACf,CAAC;IACDC,KAAK,EAAE;MACLF,WAAW,EAAE,cAAc;MAC3BC,WAAW,EAAE;IACf,CAAC;IACDE,MAAM,EAAE;MACNF,WAAW,EAAE;IACf;EACF;AACF,CAAC,CAAC;;AAEF;AACAb,MAAM,CAACgB,UAAU,CAAC,CAAC;AAEnB,eAAehB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}