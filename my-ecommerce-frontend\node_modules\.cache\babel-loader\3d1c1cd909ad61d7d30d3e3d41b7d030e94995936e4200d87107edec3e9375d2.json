{"ast": null, "code": "var _jsxFileName = \"D:\\\\SCAAND_PRO\\\\my-ecommerce-frontend\\\\src\\\\components\\\\Button.js\";\nimport '../styles/Button.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Button({\n  text,\n  onClick,\n  type = 'button',\n  className = '',\n  disabled = false,\n  'data-testid': dataTestId\n}) {\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    type: type,\n    className: `custom-button ${className}`,\n    onClick: onClick,\n    disabled: disabled,\n    \"data-testid\": dataTestId,\n    children: text\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n}\n_c = Button;\nexport default Button;\nvar _c;\n$RefreshReg$(_c, \"Button\");", "map": {"version": 3, "names": ["jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "text", "onClick", "type", "className", "disabled", "dataTestId", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/SCAAND_PRO/my-ecommerce-frontend/src/components/Button.js"], "sourcesContent": ["import '../styles/Button.css';\r\n\r\nfunction Button({ text, onClick, type = 'button', className = '', disabled = false, 'data-testid': dataTestId }) {\r\n  return (\r\n    <button\r\n      type={type}\r\n      className={`custom-button ${className}`}\r\n      onClick={onClick}\r\n      disabled={disabled}\r\n      data-testid={dataTestId}\r\n    >\r\n      {text}\r\n    </button>\r\n  );\r\n}\r\n\r\nexport default Button;\r\n"], "mappings": ";AAAA,OAAO,sBAAsB;AAAC,SAAAA,MAAA,IAAAC,OAAA;AAE9B,SAASC,MAAMA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC,IAAI,GAAG,QAAQ;EAAEC,SAAS,GAAG,EAAE;EAAEC,QAAQ,GAAG,KAAK;EAAE,aAAa,EAAEC;AAAW,CAAC,EAAE;EAC/G,oBACEP,OAAA;IACEI,IAAI,EAAEA,IAAK;IACXC,SAAS,EAAE,iBAAiBA,SAAS,EAAG;IACxCF,OAAO,EAAEA,OAAQ;IACjBG,QAAQ,EAAEA,QAAS;IACnB,eAAaC,UAAW;IAAAC,QAAA,EAEvBN;EAAI;IAAAO,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEb;AAACC,EAAA,GAZQZ,MAAM;AAcf,eAAeA,MAAM;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}