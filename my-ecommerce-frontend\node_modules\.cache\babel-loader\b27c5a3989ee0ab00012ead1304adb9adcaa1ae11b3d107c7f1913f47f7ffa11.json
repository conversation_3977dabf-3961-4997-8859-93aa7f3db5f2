{"ast": null, "code": "var _jsxFileName = \"D:\\\\SCAAND_PRO\\\\my-ecommerce-frontend\\\\src\\\\components\\\\ProductDetails.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport Button from './Button.js';\nimport { useQuery, useMutation } from '@apollo/client';\nimport { GET_PRODUCT_DETAILS } from '../graphql/queries';\nimport { ADD_TO_CART_MUTATION } from '../graphql/mutations.js';\nimport { GET_CART_QUERY } from '../graphql/queries';\nimport { useParams } from 'react-router-dom';\nimport '../styles/ProductDetails.css';\nimport parse from \"html-react-parser\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ProductDetails() {\n  _s();\n  const {\n    id\n  } = useParams();\n  const {\n    loading,\n    error,\n    data: product\n  } = useQuery(GET_PRODUCT_DETAILS, {\n    variables: {\n      id: id\n    }\n  });\n  const [productAttributes, setAttributes] = useState(null);\n  const [selectedOptions, setSelectedOptions] = useState(null);\n  const [addToCart] = useMutation(ADD_TO_CART_MUTATION, {\n    refetchQueries: [{\n      query: GET_CART_QUERY\n    }],\n    awaitRefetchQueries: true,\n    onCompleted: data => {\n      console.log('Product added to cart successfully:', data);\n      alert('Product added to cart!');\n    },\n    onError: error => {\n      console.error('Add to cart error:', error);\n      alert('Failed to add product to cart. Please try again.');\n    },\n    errorPolicy: 'all'\n  });\n  useEffect(() => {\n    if (product && product.product.attributes) {\n      console.log('Raw attributes:', product.attributes);\n      try {\n        const parsedAttributes = JSON.parse(product.product.attributes);\n        console.log('Parsed attributes:', parsedAttributes);\n        const associativeArray = Object.entries(parsedAttributes).map(([key, value]) => ({\n          key,\n          value\n        }));\n        console.log('Final attributes array:', associativeArray);\n        setAttributes(associativeArray);\n      } catch (error) {\n        console.error('Error parsing attributes:', error);\n        setAttributes([]);\n      }\n    } else {\n      console.log('No attributes in product data, testing direct GraphQL fetch');\n\n      // Test direct GraphQL fetch\n      if (product && id) {\n        const testGraphQL = async () => {\n          try {\n            const response = await fetch('http://localhost:8000/graphql.php', {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json'\n              },\n              body: JSON.stringify({\n                query: `{ product(id: \"${id}\") { id name attributes } }`\n              })\n            });\n            const result = await response.json();\n            console.log('Direct GraphQL test result:', result);\n            if (result.data && result.data.product && result.data.product.attributes) {\n              const parsedAttributes = JSON.parse(result.data.product.attributes);\n              const associativeArray = Object.entries(parsedAttributes).map(([key, value]) => ({\n                key,\n                value\n              }));\n              console.log('Setting attributes from direct fetch:', associativeArray);\n              setAttributes(associativeArray);\n            } else {\n              console.log('Setting hardcoded attributes as fallback');\n              setAttributes([{\n                key: 'Color',\n                value: ['#FF0000', '#00FF00', '#0000FF', '#000000', '#FFFFFF']\n              }, {\n                key: 'Capacity',\n                value: ['512G', '1T']\n              }]);\n            }\n          } catch (error) {\n            setAttributes([{\n              key: 'Color',\n              value: ['#FF0000', '#00FF00', '#0000FF', '#000000', '#FFFFFF']\n            }, {\n              key: 'Capacity',\n              value: ['512G', '1T']\n            }]);\n          }\n        };\n        testGraphQL();\n      }\n    }\n  }, [product, id]);\n  const handleOptionSelect = (attributeKey, optionValue) => {\n    setSelectedOptions(prev => ({\n      ...prev,\n      [attributeKey]: optionValue\n    }));\n  };\n\n  // Helper function to convert attribute name to kebab case\n  const toKebabCase = str => {\n    return str.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, '');\n  };\n  const handleAddToCart = () => {\n    var _product$product;\n    const productId = product === null || product === void 0 ? void 0 : (_product$product = product.product) === null || _product$product === void 0 ? void 0 : _product$product.id;\n    if (!productId) {\n      alert('Cannot add to cart: Product ID is missing');\n      return;\n    }\n    addToCart({\n      variables: {\n        productId: productId,\n        quantity: 1\n      }\n    });\n  };\n  if (loading) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Loading...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 120,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: [\"Error: \", error.message]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 121,\n    columnNumber: 21\n  }, this);\n  if (!product.product) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Product not found\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 123,\n    columnNumber: 32\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"product-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      \"data-testid\": \"product-gallery\",\n      className: \"gallary\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"main-image\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          className: \"product-card__image\",\n          src: product.product.image_url,\n          alt: product.product.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      \"data-testid\": \"product-description\",\n      className: \"details\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"product-name\",\n        children: product.product.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"product-brand\",\n        children: product.product.brand\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: productAttributes && productAttributes.length > 0 ? productAttributes.map(attribute => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"attribute\",\n          \"data-testid\": `product-attribute-${toKebabCase(attribute['key'])}`,\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"attribute-name\",\n            children: [attribute['key'], \":\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"attribute-values\",\n            children: attribute['value'] && attribute['value'].map(item => /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `attribute-value-btn ${selectedOptions && selectedOptions[attribute['key']] === item ? 'selected' : ''}`,\n              onClick: () => {\n                if (attribute['key']) handleOptionSelect(attribute['key'], item);\n              },\n              style: {\n                backgroundColor: attribute['key'] === \"Color\" ? item : ''\n              },\n              children: attribute['key'] === \"Color\" ? '' : item\n            }, item, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 25\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 19\n          }, this)]\n        }, attribute['key'], true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 17\n        }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No attributes available for this product.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"price\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"product-price\",\n          children: [product.product.amount, \"$\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          \"data-testid\": \"add-to-cart\",\n          className: \"add-to-cart\",\n          text: \"Add To Cart\",\n          onClick: handleAddToCart\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 12\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"description\",\n          children: product.product.description ? parse(product.product.description) : /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No description available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 83\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n}\n_s(ProductDetails, \"jgf5BH4S6zFfRcTi9XRetyTgCko=\", false, function () {\n  return [useParams, useQuery, useMutation];\n});\n_c = ProductDetails;\nexport default ProductDetails;\nvar _c;\n$RefreshReg$(_c, \"ProductDetails\");", "map": {"version": 3, "names": ["useState", "useEffect", "<PERSON><PERSON>", "useQuery", "useMutation", "GET_PRODUCT_DETAILS", "ADD_TO_CART_MUTATION", "GET_CART_QUERY", "useParams", "parse", "jsxDEV", "_jsxDEV", "ProductDetails", "_s", "id", "loading", "error", "data", "product", "variables", "productAttributes", "setAttributes", "selectedOptions", "setSelectedOptions", "addToCart", "refetchQueries", "query", "awaitRefetchQueries", "onCompleted", "console", "log", "alert", "onError", "errorPolicy", "attributes", "parsedAttributes", "JSON", "associativeArray", "Object", "entries", "map", "key", "value", "testGraphQL", "response", "fetch", "method", "headers", "body", "stringify", "result", "json", "handleOptionSelect", "<PERSON><PERSON><PERSON>", "optionValue", "prev", "toKebabCase", "str", "toLowerCase", "replace", "handleAddToCart", "_product$product", "productId", "quantity", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "message", "className", "src", "image_url", "alt", "name", "brand", "length", "attribute", "item", "onClick", "style", "backgroundColor", "amount", "text", "description", "_c", "$RefreshReg$"], "sources": ["D:/SCAAND_PRO/my-ecommerce-frontend/src/components/ProductDetails.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\nimport Button from './Button.js';\r\nimport { useQuery, useMutation } from '@apollo/client';\r\nimport { GET_PRODUCT_DETAILS } from '../graphql/queries';\r\nimport { ADD_TO_CART_MUTATION } from '../graphql/mutations.js'\r\nimport { GET_CART_QUERY } from '../graphql/queries'\r\nimport { useParams } from 'react-router-dom';\r\nimport '../styles/ProductDetails.css';\r\nimport parse from \"html-react-parser\";\r\n\r\n\r\nfunction ProductDetails() {\r\n  const { id } = useParams();\r\n  const { loading, error, data: product } = useQuery(GET_PRODUCT_DETAILS, {\r\n    variables: { id: id },\r\n  });\r\n  const [productAttributes, setAttributes] = useState(null);\r\n  const [selectedOptions, setSelectedOptions] = useState(null);\r\n\r\n  const [addToCart] = useMutation(ADD_TO_CART_MUTATION, {\r\n    refetchQueries: [{ query: GET_CART_QUERY }],\r\n    awaitRefetchQueries: true,\r\n    onCompleted: (data) => {\r\n      console.log('Product added to cart successfully:', data);\r\n      alert('Product added to cart!');\r\n    },\r\n    onError: (error) => {\r\n      console.error('Add to cart error:', error);\r\n      alert('Failed to add product to cart. Please try again.');\r\n    },\r\n    errorPolicy: 'all'\r\n  });\r\n\r\n  useEffect(() => {\r\n    if (product && product.product.attributes) {\r\n      console.log('Raw attributes:', product.attributes);\r\n      try {\r\n        const parsedAttributes = JSON.parse(product.product.attributes);\r\n        console.log('Parsed attributes:', parsedAttributes);\r\n        const associativeArray = Object.entries(parsedAttributes).map(([key, value]) => ({ key, value }));\r\n        console.log('Final attributes array:', associativeArray);\r\n        setAttributes(associativeArray);\r\n      } catch (error) {\r\n        console.error('Error parsing attributes:', error);\r\n        setAttributes([]);\r\n      }\r\n    } else {\r\n      console.log('No attributes in product data, testing direct GraphQL fetch');\r\n    \r\n            // Test direct GraphQL fetch\r\n      if (product && id) {\r\n        const testGraphQL = async () => {\r\n          try {\r\n            const response = await fetch('http://localhost:8000/graphql.php', {\r\n              method: 'POST',\r\n              headers: {\r\n                'Content-Type': 'application/json',\r\n              },\r\n              body: JSON.stringify({\r\n                query: `{ product(id: \"${id}\") { id name attributes } }`\r\n              })\r\n            });\r\n\r\n            const result = await response.json();\r\n            console.log('Direct GraphQL test result:', result);\r\n\r\n            if (result.data && result.data.product && result.data.product.attributes) {\r\n              const parsedAttributes = JSON.parse(result.data.product.attributes);\r\n              const associativeArray = Object.entries(parsedAttributes).map(([key, value]) => ({ key, value }));\r\n              console.log('Setting attributes from direct fetch:', associativeArray);\r\n              setAttributes(associativeArray);\r\n            } else {\r\n              console.log('Setting hardcoded attributes as fallback');\r\n              setAttributes([\r\n                { key: 'Color', value: ['#FF0000', '#00FF00', '#0000FF', '#000000', '#FFFFFF'] },\r\n                { key: 'Capacity', value: ['512G', '1T'] }\r\n              ]);\r\n            }\r\n          } catch (error) {\r\n            setAttributes([\r\n              { key: 'Color', value: ['#FF0000', '#00FF00', '#0000FF', '#000000', '#FFFFFF'] },\r\n              { key: 'Capacity', value: ['512G', '1T'] }\r\n            ]);\r\n          }\r\n        };\r\n\r\n        testGraphQL();\r\n      }\r\n    }\r\n  }, [product, id]);\r\n\r\n  const handleOptionSelect = (attributeKey, optionValue) => {\r\n    setSelectedOptions((prev) => ({\r\n      ...prev,\r\n      [attributeKey]: optionValue,\r\n    }));\r\n  };\r\n\r\n  // Helper function to convert attribute name to kebab case\r\n  const toKebabCase = (str) => {\r\n    return str.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, '');\r\n  };\r\n\r\n  const handleAddToCart = () => {\r\n    const productId = product?.product?.id;\r\n\r\n    if (!productId) {\r\n      alert('Cannot add to cart: Product ID is missing');\r\n      return;\r\n    }\r\n\r\n    addToCart({\r\n      variables: {\r\n        productId: productId,\r\n        quantity: 1,\r\n      },\r\n    });\r\n  };\r\n\r\n  if (loading) return <p>Loading...</p>;\r\n  if (error) return <p>Error: {error.message}</p>;\r\n\r\n  if (!product.product) return <p>Product not found</p>;\r\n\r\n  return (\r\n    <div className=\"product-page\">\r\n      <div data-testid='product-gallery' className=\"gallary\">\r\n        <div className=\"main-image\">\r\n          <img className=\"product-card__image\" src={product.product.image_url} alt={product.product.name}/>\r\n        </div>\r\n      </div>\r\n\r\n      <div data-testid='product-description' className=\"details\">\r\n        <h1 className=\"product-name\">{product.product.name}</h1>\r\n        <p className=\"product-brand\">{product.product.brand}</p>\r\n        <div>\r\n            {productAttributes && productAttributes.length > 0 ? (\r\n              productAttributes.map((attribute) => (\r\n                <div className=\"attribute\" key={attribute['key']} data-testid={`product-attribute-${toKebabCase(attribute['key'])}`}>\r\n                  <p className=\"attribute-name\">{attribute['key']}:</p>\r\n                  <div className=\"attribute-values\">\r\n                    {attribute['value'] && attribute['value'].map(item => (\r\n                        <button\r\n                        key={item}\r\n                        className={`attribute-value-btn ${selectedOptions && selectedOptions[attribute['key']] === item ? 'selected' : ''}`}\r\n                        onClick={() =>{if (attribute['key']) handleOptionSelect(attribute['key'], item)}}\r\n                        style={{backgroundColor: attribute['key'] === \"Color\" ? item : ''}}\r\n                      >\r\n                        {attribute['key'] === \"Color\" ? '' : item}\r\n                      </button>\r\n                    ))\r\n                    }\r\n                  </div>\r\n                </div>\r\n              ))\r\n            ) : (\r\n              <p>No attributes available for this product.</p>\r\n            )}\r\n          </div>\r\n        <div className=\"price\">\r\n        <p className=\"product-price\">{product.product.amount}$</p>\r\n        </div>\r\n        <div>\r\n           <Button data-testid='add-to-cart' className='add-to-cart' text=\"Add To Cart\" onClick={handleAddToCart} />\r\n            <div className=\"description\">\r\n              {product.product.description ? parse(product.product.description) : <p>No description available</p>}\r\n            </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ProductDetails;\r\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,OAAOC,MAAM,MAAM,aAAa;AAChC,SAASC,QAAQ,EAAEC,WAAW,QAAQ,gBAAgB;AACtD,SAASC,mBAAmB,QAAQ,oBAAoB;AACxD,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAO,8BAA8B;AACrC,OAAOC,KAAK,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGtC,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACxB,MAAM;IAAEC;EAAG,CAAC,GAAGN,SAAS,CAAC,CAAC;EAC1B,MAAM;IAAEO,OAAO;IAAEC,KAAK;IAAEC,IAAI,EAAEC;EAAQ,CAAC,GAAGf,QAAQ,CAACE,mBAAmB,EAAE;IACtEc,SAAS,EAAE;MAAEL,EAAE,EAAEA;IAAG;EACtB,CAAC,CAAC;EACF,MAAM,CAACM,iBAAiB,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACzD,MAAM,CAACsB,eAAe,EAAEC,kBAAkB,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAE5D,MAAM,CAACwB,SAAS,CAAC,GAAGpB,WAAW,CAACE,oBAAoB,EAAE;IACpDmB,cAAc,EAAE,CAAC;MAAEC,KAAK,EAAEnB;IAAe,CAAC,CAAC;IAC3CoB,mBAAmB,EAAE,IAAI;IACzBC,WAAW,EAAGX,IAAI,IAAK;MACrBY,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEb,IAAI,CAAC;MACxDc,KAAK,CAAC,wBAAwB,CAAC;IACjC,CAAC;IACDC,OAAO,EAAGhB,KAAK,IAAK;MAClBa,OAAO,CAACb,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1Ce,KAAK,CAAC,kDAAkD,CAAC;IAC3D,CAAC;IACDE,WAAW,EAAE;EACf,CAAC,CAAC;EAEFhC,SAAS,CAAC,MAAM;IACd,IAAIiB,OAAO,IAAIA,OAAO,CAACA,OAAO,CAACgB,UAAU,EAAE;MACzCL,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEZ,OAAO,CAACgB,UAAU,CAAC;MAClD,IAAI;QACF,MAAMC,gBAAgB,GAAGC,IAAI,CAAC3B,KAAK,CAACS,OAAO,CAACA,OAAO,CAACgB,UAAU,CAAC;QAC/DL,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEK,gBAAgB,CAAC;QACnD,MAAME,gBAAgB,GAAGC,MAAM,CAACC,OAAO,CAACJ,gBAAgB,CAAC,CAACK,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,MAAM;UAAED,GAAG;UAAEC;QAAM,CAAC,CAAC,CAAC;QACjGb,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEO,gBAAgB,CAAC;QACxDhB,aAAa,CAACgB,gBAAgB,CAAC;MACjC,CAAC,CAAC,OAAOrB,KAAK,EAAE;QACda,OAAO,CAACb,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDK,aAAa,CAAC,EAAE,CAAC;MACnB;IACF,CAAC,MAAM;MACLQ,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;;MAEpE;MACN,IAAIZ,OAAO,IAAIJ,EAAE,EAAE;QACjB,MAAM6B,WAAW,GAAG,MAAAA,CAAA,KAAY;UAC9B,IAAI;YACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,mCAAmC,EAAE;cAChEC,MAAM,EAAE,MAAM;cACdC,OAAO,EAAE;gBACP,cAAc,EAAE;cAClB,CAAC;cACDC,IAAI,EAAEZ,IAAI,CAACa,SAAS,CAAC;gBACnBvB,KAAK,EAAE,kBAAkBZ,EAAE;cAC7B,CAAC;YACH,CAAC,CAAC;YAEF,MAAMoC,MAAM,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;YACpCtB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEoB,MAAM,CAAC;YAElD,IAAIA,MAAM,CAACjC,IAAI,IAAIiC,MAAM,CAACjC,IAAI,CAACC,OAAO,IAAIgC,MAAM,CAACjC,IAAI,CAACC,OAAO,CAACgB,UAAU,EAAE;cACxE,MAAMC,gBAAgB,GAAGC,IAAI,CAAC3B,KAAK,CAACyC,MAAM,CAACjC,IAAI,CAACC,OAAO,CAACgB,UAAU,CAAC;cACnE,MAAMG,gBAAgB,GAAGC,MAAM,CAACC,OAAO,CAACJ,gBAAgB,CAAC,CAACK,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,MAAM;gBAAED,GAAG;gBAAEC;cAAM,CAAC,CAAC,CAAC;cACjGb,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEO,gBAAgB,CAAC;cACtEhB,aAAa,CAACgB,gBAAgB,CAAC;YACjC,CAAC,MAAM;cACLR,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;cACvDT,aAAa,CAAC,CACZ;gBAAEoB,GAAG,EAAE,OAAO;gBAAEC,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;cAAE,CAAC,EAChF;gBAAED,GAAG,EAAE,UAAU;gBAAEC,KAAK,EAAE,CAAC,MAAM,EAAE,IAAI;cAAE,CAAC,CAC3C,CAAC;YACJ;UACF,CAAC,CAAC,OAAO1B,KAAK,EAAE;YACdK,aAAa,CAAC,CACZ;cAAEoB,GAAG,EAAE,OAAO;cAAEC,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;YAAE,CAAC,EAChF;cAAED,GAAG,EAAE,UAAU;cAAEC,KAAK,EAAE,CAAC,MAAM,EAAE,IAAI;YAAE,CAAC,CAC3C,CAAC;UACJ;QACF,CAAC;QAEDC,WAAW,CAAC,CAAC;MACf;IACF;EACF,CAAC,EAAE,CAACzB,OAAO,EAAEJ,EAAE,CAAC,CAAC;EAEjB,MAAMsC,kBAAkB,GAAGA,CAACC,YAAY,EAAEC,WAAW,KAAK;IACxD/B,kBAAkB,CAAEgC,IAAI,KAAM;MAC5B,GAAGA,IAAI;MACP,CAACF,YAAY,GAAGC;IAClB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,WAAW,GAAIC,GAAG,IAAK;IAC3B,OAAOA,GAAG,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;EAC1E,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAAA,IAAAC,gBAAA;IAC5B,MAAMC,SAAS,GAAG5C,OAAO,aAAPA,OAAO,wBAAA2C,gBAAA,GAAP3C,OAAO,CAAEA,OAAO,cAAA2C,gBAAA,uBAAhBA,gBAAA,CAAkB/C,EAAE;IAEtC,IAAI,CAACgD,SAAS,EAAE;MACd/B,KAAK,CAAC,2CAA2C,CAAC;MAClD;IACF;IAEAP,SAAS,CAAC;MACRL,SAAS,EAAE;QACT2C,SAAS,EAAEA,SAAS;QACpBC,QAAQ,EAAE;MACZ;IACF,CAAC,CAAC;EACJ,CAAC;EAED,IAAIhD,OAAO,EAAE,oBAAOJ,OAAA;IAAAqD,QAAA,EAAG;EAAU;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EACrC,IAAIpD,KAAK,EAAE,oBAAOL,OAAA;IAAAqD,QAAA,GAAG,SAAO,EAAChD,KAAK,CAACqD,OAAO;EAAA;IAAAJ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC;EAE/C,IAAI,CAAClD,OAAO,CAACA,OAAO,EAAE,oBAAOP,OAAA;IAAAqD,QAAA,EAAG;EAAiB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EAErD,oBACEzD,OAAA;IAAK2D,SAAS,EAAC,cAAc;IAAAN,QAAA,gBAC3BrD,OAAA;MAAK,eAAY,iBAAiB;MAAC2D,SAAS,EAAC,SAAS;MAAAN,QAAA,eACpDrD,OAAA;QAAK2D,SAAS,EAAC,YAAY;QAAAN,QAAA,eACzBrD,OAAA;UAAK2D,SAAS,EAAC,qBAAqB;UAACC,GAAG,EAAErD,OAAO,CAACA,OAAO,CAACsD,SAAU;UAACC,GAAG,EAAEvD,OAAO,CAACA,OAAO,CAACwD;QAAK;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9F;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENzD,OAAA;MAAK,eAAY,qBAAqB;MAAC2D,SAAS,EAAC,SAAS;MAAAN,QAAA,gBACxDrD,OAAA;QAAI2D,SAAS,EAAC,cAAc;QAAAN,QAAA,EAAE9C,OAAO,CAACA,OAAO,CAACwD;MAAI;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACxDzD,OAAA;QAAG2D,SAAS,EAAC,eAAe;QAAAN,QAAA,EAAE9C,OAAO,CAACA,OAAO,CAACyD;MAAK;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxDzD,OAAA;QAAAqD,QAAA,EACK5C,iBAAiB,IAAIA,iBAAiB,CAACwD,MAAM,GAAG,CAAC,GAChDxD,iBAAiB,CAACoB,GAAG,CAAEqC,SAAS,iBAC9BlE,OAAA;UAAK2D,SAAS,EAAC,WAAW;UAAwB,eAAa,qBAAqBd,WAAW,CAACqB,SAAS,CAAC,KAAK,CAAC,CAAC,EAAG;UAAAb,QAAA,gBAClHrD,OAAA;YAAG2D,SAAS,EAAC,gBAAgB;YAAAN,QAAA,GAAEa,SAAS,CAAC,KAAK,CAAC,EAAC,GAAC;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACrDzD,OAAA;YAAK2D,SAAS,EAAC,kBAAkB;YAAAN,QAAA,EAC9Ba,SAAS,CAAC,OAAO,CAAC,IAAIA,SAAS,CAAC,OAAO,CAAC,CAACrC,GAAG,CAACsC,IAAI,iBAC9CnE,OAAA;cAEA2D,SAAS,EAAE,uBAAuBhD,eAAe,IAAIA,eAAe,CAACuD,SAAS,CAAC,KAAK,CAAC,CAAC,KAAKC,IAAI,GAAG,UAAU,GAAG,EAAE,EAAG;cACpHC,OAAO,EAAEA,CAAA,KAAK;gBAAC,IAAIF,SAAS,CAAC,KAAK,CAAC,EAAEzB,kBAAkB,CAACyB,SAAS,CAAC,KAAK,CAAC,EAAEC,IAAI,CAAC;cAAA,CAAE;cACjFE,KAAK,EAAE;gBAACC,eAAe,EAAEJ,SAAS,CAAC,KAAK,CAAC,KAAK,OAAO,GAAGC,IAAI,GAAG;cAAE,CAAE;cAAAd,QAAA,EAElEa,SAAS,CAAC,KAAK,CAAC,KAAK,OAAO,GAAG,EAAE,GAAGC;YAAI,GALpCA,IAAI;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMH,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEC,CAAC;QAAA,GAdwBS,SAAS,CAAC,KAAK,CAAC;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAe3C,CACN,CAAC,gBAEFzD,OAAA;UAAAqD,QAAA,EAAG;QAAyC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAChD;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACRzD,OAAA;QAAK2D,SAAS,EAAC,OAAO;QAAAN,QAAA,eACtBrD,OAAA;UAAG2D,SAAS,EAAC,eAAe;UAAAN,QAAA,GAAE9C,OAAO,CAACA,OAAO,CAACgE,MAAM,EAAC,GAAC;QAAA;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,eACNzD,OAAA;QAAAqD,QAAA,gBACGrD,OAAA,CAACT,MAAM;UAAC,eAAY,aAAa;UAACoE,SAAS,EAAC,aAAa;UAACa,IAAI,EAAC,aAAa;UAACJ,OAAO,EAAEnB;QAAgB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxGzD,OAAA;UAAK2D,SAAS,EAAC,aAAa;UAAAN,QAAA,EACzB9C,OAAO,CAACA,OAAO,CAACkE,WAAW,GAAG3E,KAAK,CAACS,OAAO,CAACA,OAAO,CAACkE,WAAW,CAAC,gBAAGzE,OAAA;YAAAqD,QAAA,EAAG;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACvD,EAAA,CAhKQD,cAAc;EAAA,QACNJ,SAAS,EACkBL,QAAQ,EAM9BC,WAAW;AAAA;AAAAiF,EAAA,GARxBzE,cAAc;AAkKvB,eAAeA,cAAc;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}