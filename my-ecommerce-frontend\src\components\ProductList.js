// src/components/ProductList.js
import { useQuery, useMutation } from '@apollo/client';
import { GET_PRODUCTS, GET_CART_QUERY } from '../graphql/queries';
import { ADD_TO_CART_MUTATION } from '../graphql/mutations';
import { Link } from 'react-router-dom';
import '../styles/ProductList.css';

function ProductList({ category_id, category }) {
  const { loading, error, data } = useQuery(GET_PRODUCTS);

  const [addToCart] = useMutation(ADD_TO_CART_MUTATION, {
    refetchQueries: [{ query: GET_CART_QUERY }],
    awaitRefetchQueries: true,
    onCompleted: () => {
      alert('Product added to cart!');
    },
    onError: (error) => {
      console.error('Add to cart error:', error);
      alert('Failed to add product to cart. Please try again.');
    },
    errorPolicy: 'all'
  });

  if (loading) return <p>Loading...</p>;
  if (error) return <p>Error: {error.message}</p>;

  const products = category
    ? data.products.filter(product => product.category_id === category_id)
    : data.products;

  // Helper function to convert product name to kebab case
  const toKebabCase = (str) => {
    return str.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
  };

  // Handle quick shop (add to cart with default options)
  const handleQuickShop = (e, productId) => {
    e.preventDefault(); // Prevent navigation to product details
    e.stopPropagation(); // Stop event bubbling

    addToCart({
      variables: {
        productId: productId,
        quantity: 1,
      },
    });
  };

  return (
    <>
    <div className="products">
      <div className="header">
        <h1>{category ? category: "All"}</h1>
      </div>
        {products.map((product) => (
          <div className='product-card' data-testid={`product-${toKebabCase(product.name)}`} key={product.id}>
            <Link className='product-link' to={`/product/${product.id}`}>
              <img className="product-card__image" src={product.image_url} alt={product.name} />
              <p className="product-card__brand">{product.brand}</p>
              <div className="product-name">{product.name}</div>
              <div className="price">
                ${product.amount}
              </div>
            </Link>

            {/* Quick Shop Button - only visible on hover */}
            <button
              className="quick-shop-btn"
              onClick={(e) => handleQuickShop(e, product.id)}
              data-testid={`add-to-cart-${toKebabCase(product.name)}`}
              title="Quick Shop"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M7 4V2C7 1.45 7.45 1 8 1H16C16.55 1 17 1.45 17 2V4H20C20.55 4 21 4.45 21 5S20.55 6 20 6H19V19C19 20.1 18.1 21 17 21H7C5.9 21 5 20.1 5 19V6H4C3.45 6 3 5.55 3 5S3.45 4 4 4H7ZM9 3V4H15V3H9ZM7 6V19H17V6H7Z" fill="white"/>
                <path d="M9 8V17H11V8H9ZM13 8V17H15V8H13Z" fill="white"/>
              </svg>
            </button>
          </div>
        ))}
    </div>
    </>
  );
}

export default ProductList;
