type Product {
    id: ID!
    name: String!
    brand: String
    description: String
    inStock: Boolean
    amount: Float
    image_url: String
    category: Category
    createdAt: String!
}

type attribute {
    id: ID!
    name: String!
    value: String!
}

type Category {
    id: ID!
    name: String!
}

type User {
  id: ID!
  username: String!
  email: String!
  token: String
}

type AuthPayload {
  user: User
  message: String!
}

type Cart {
    id: ID!
    product: Product
    quantity: Int
}

type Query {
    product(id: ID!): Product
    products: [Product]
    categories: [Category]
    attributes: [attribute]
    cart: [Cart]
}

type Mutation {
    createProduct(name: String!, price: Float!, categoryId: ID!): Product
    login(username: String!, password: String!): AuthPayload
    signup(username: String!, email: String!, password: String!): AuthPayload
    addToCart(productId: ID!, quantity: Int!): Cart
    updateCart(itemId: ID!, quantityChange: Int!): Cart
    removeFromCart(itemId: ID!): Cart
    placeOrder: OrderResult
}

type OrderResult {
    success: Boolean!
    message: String!
}
