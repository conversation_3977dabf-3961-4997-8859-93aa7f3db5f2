{"ast": null, "code": "var _jsxFileName = \"D:\\\\SCAAND_PRO\\\\my-ecommerce-frontend\\\\src\\\\index.js\";\nimport ReactDOM from 'react-dom/client';\nimport { ApolloProvider } from '@apollo/client';\nimport client from './apolloClient';\nimport App from './App';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(/*#__PURE__*/_jsxDEV(ApolloProvider, {\n  client: client,\n  children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 9,\n  columnNumber: 3\n}, this));", "map": {"version": 3, "names": ["ReactDOM", "ApolloProvider", "client", "App", "jsxDEV", "_jsxDEV", "root", "createRoot", "document", "getElementById", "render", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["D:/SCAAND_PRO/my-ecommerce-frontend/src/index.js"], "sourcesContent": ["import ReactDOM from 'react-dom/client';\nimport { ApolloProvider } from '@apollo/client';\nimport client from './apolloClient';\nimport App from './App';\nimport './index.css';\n\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(\n  <ApolloProvider client={client}>\n    <App />\n  </ApolloProvider>\n);\n"], "mappings": ";AAAA,OAAOA,QAAQ,MAAM,kBAAkB;AACvC,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAMC,IAAI,GAAGN,QAAQ,CAACO,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC;AACjEH,IAAI,CAACI,MAAM,cACTL,OAAA,CAACJ,cAAc;EAACC,MAAM,EAAEA,MAAO;EAAAS,QAAA,eAC7BN,OAAA,CAACF,GAAG;IAAAS,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACO,CAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}