{"ast": null, "code": "var _jsxFileName = \"D:\\\\SCAAND_PRO\\\\my-ecommerce-frontend\\\\src\\\\components\\\\CartPopup.js\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport { useQuery, useMutation } from '@apollo/client';\nimport { GET_CART_QUERY } from '../graphql/queries';\nimport { UPDATE_CART_MUTATION } from '../graphql/mutations';\nimport '../styles/CartPopup.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CartPopup({\n  isOpen,\n  closePopup,\n  cartItems\n}) {\n  _s();\n  const {\n    loading,\n    error,\n    data,\n    refetch\n  } = useQuery(GET_CART_QUERY, {\n    fetchPolicy: 'cache-and-network',\n    notifyOnNetworkStatusChange: true\n  });\n\n  // State for managing selected options for each cart item\n  const [selectedOptions, setSelectedOptions] = useState({});\n\n  // Mutations\n  const [updateCartItem] = useMutation(UPDATE_CART_MUTATION, {\n    onError: error => {\n      console.error('Update cart error:', error);\n    },\n    errorPolicy: 'all'\n  });\n\n  // Prevent rendering if the popup is closed\n  if (!isOpen) return null;\n\n  // Show loading state\n  if (loading) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"cart-popup\",\n    children: \"Loading...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 23\n  }, this);\n\n  // Show error state\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"cart-popup\",\n    children: [\"Error loading cart: \", error.message]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 21\n  }, this);\n\n  // Use data from query instead of props for consistency\n  const actualCartItems = (data === null || data === void 0 ? void 0 : data.cart) || cartItems || [];\n  const calculateTotal = () => {\n    if (!actualCartItems || !Array.isArray(actualCartItems)) return '0.00';\n    return actualCartItems.reduce((total, item) => total + item.product.price * item.quantity, 0).toFixed(2);\n  };\n  const getTotalItemCount = () => {\n    if (!actualCartItems || !Array.isArray(actualCartItems)) return 0;\n    return actualCartItems.reduce((total, item) => total + item.quantity, 0);\n  };\n\n  // Helper function to convert attribute name to kebab case\n  const toKebabCase = str => {\n    return str.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, '');\n  };\n\n  // Handle size selection\n  const handleSizeSelect = (itemId, size) => {\n    setSelectedOptions(prev => ({\n      ...prev,\n      [itemId]: {\n        ...prev[itemId],\n        size: size\n      }\n    }));\n  };\n\n  // Handle color selection\n  const handleColorSelect = (itemId, color) => {\n    setSelectedOptions(prev => ({\n      ...prev,\n      [itemId]: {\n        ...prev[itemId],\n        color: color\n      }\n    }));\n  };\n\n  // Get selected options for an item with defaults\n  const getSelectedOptions = (itemId, productAttributes) => {\n    if (!selectedOptions[itemId]) {\n      // Set default selections based on available attributes\n      const parsedAttributes = parseProductAttributes(productAttributes);\n      const availableSizes = parsedAttributes.Size || parsedAttributes.size || ['S'];\n      const availableColors = parsedAttributes.Color || parsedAttributes.color || ['#2B5D31'];\n      return {\n        size: availableSizes[0] || 'S',\n        color: availableColors[0] || '#2B5D31'\n      };\n    }\n    return selectedOptions[itemId];\n  };\n\n  // Parse product attributes\n  const parseProductAttributes = attributesString => {\n    try {\n      if (!attributesString) return {};\n      return JSON.parse(attributesString);\n    } catch (error) {\n      return {};\n    }\n  };\n\n  // Get attribute options for display\n  const getAttributeOptions = (attributes, attributeName) => {\n    const parsedAttributes = parseProductAttributes(attributes);\n    return parsedAttributes[attributeName] || [];\n  };\n\n  // Increase Quantity Handler\n  const handleIncreaseQuantity = itemId => {\n    updateCartItem({\n      variables: {\n        itemId,\n        quantityChange: 1\n      },\n      refetchQueries: [{\n        query: GET_CART_QUERY\n      }]\n    }).catch(error => {\n      console.error('Error increasing quantity:', error);\n      alert('Failed to update quantity. Please try again.');\n    });\n  };\n\n  // Decrease Quantity Handler - now removes item when quantity reaches 0\n  const handleDecreaseQuantity = (itemId, currentQuantity) => {\n    if (currentQuantity > 1) {\n      // Decrease quantity by 1\n      updateCartItem({\n        variables: {\n          itemId,\n          quantityChange: -1\n        },\n        refetchQueries: [{\n          query: GET_CART_QUERY\n        }]\n      }).catch(error => {\n        console.error('Error decreasing quantity:', error);\n        alert('Failed to update quantity. Please try again.');\n      });\n    } else {\n      // When quantity is 1, decrease it to 0 which should remove the item\n      updateCartItem({\n        variables: {\n          itemId,\n          quantityChange: -1\n        },\n        refetchQueries: [{\n          query: GET_CART_QUERY\n        }],\n        awaitRefetchQueries: true\n      }).catch(error => {\n        console.error('Error removing item via quantity decrease:', error);\n        alert('Failed to remove item. Please try again.');\n      });\n    }\n  };\n\n  // Place Order Handler using dedicated endpoint\n  const handlePlaceOrder = async () => {\n    try {\n      var _result$data, _result$data$placeOrd;\n      const response = await fetch('http://localhost:8000/place_order_endpoint.php', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          query: `\n            mutation PlaceOrder {\n              placeOrder {\n                success\n                message\n              }\n            }\n          `\n        })\n      });\n      const result = await response.json();\n      if ((_result$data = result.data) !== null && _result$data !== void 0 && (_result$data$placeOrd = _result$data.placeOrder) !== null && _result$data$placeOrd !== void 0 && _result$data$placeOrd.success) {\n        // Refetch the cart data to update UI\n        await refetch();\n        alert('Order placed successfully!');\n        closePopup(); // Close the cart popup\n      } else {\n        var _result$data2, _result$data2$placeOr;\n        throw new Error(((_result$data2 = result.data) === null || _result$data2 === void 0 ? void 0 : (_result$data2$placeOr = _result$data2.placeOrder) === null || _result$data2$placeOr === void 0 ? void 0 : _result$data2$placeOr.message) || 'Order failed');\n      }\n    } catch (err) {\n      console.error('Place order error:', err);\n      alert(`Failed to place order: ${err.message}`);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modalBackground\",\n    onClick: closePopup,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modalContainer\",\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cart-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\"My Bag, \", getTotalItemCount(), \" items\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-btn\",\n          onClick: closePopup,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), !actualCartItems || actualCartItems.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Your cart is empty.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cart-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cart-items-container\",\n          children: actualCartItems === null || actualCartItems === void 0 ? void 0 : actualCartItems.map((item, index) => {\n            const itemOptions = getSelectedOptions(item.id, item.product.attributes);\n\n            // Get available sizes and colors from product attributes\n            const availableSizes = getAttributeOptions(item.product.attributes, 'Size') || getAttributeOptions(item.product.attributes, 'size') || ['XS', 'S', 'M', 'L']; // fallback\n            const availableColors = getAttributeOptions(item.product.attributes, 'Color') || getAttributeOptions(item.product.attributes, 'color') || ['#C4D79B', '#2B5D31', '#0F4C3A']; // fallback\n\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"cart-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"cart-item-left\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"product-name\",\n                  children: item.product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"product-price\",\n                  \"data-testid\": \"cart-item-amount\",\n                  children: [\"$\", item.product.price]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 23\n                }, this), availableSizes.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"size-section\",\n                  \"data-testid\": `cart-item-attribute-${toKebabCase('Size')}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"size-label\",\n                    children: \"Size:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"size-options\",\n                    children: availableSizes.map(size => /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `size-btn ${itemOptions.size === size ? 'selected' : ''} non-clickable`,\n                      \"data-testid\": `cart-item-attribute-${toKebabCase('Size')}-${toKebabCase(size)}${itemOptions.size === size ? '-selected' : ''}`,\n                      children: size\n                    }, size, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 223,\n                      columnNumber: 31\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 25\n                }, this), availableColors.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"color-section\",\n                  \"data-testid\": `cart-item-attribute-${toKebabCase('Color')}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"color-label\",\n                    children: \"Color:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"color-options\",\n                    children: availableColors.map(color => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `color-circle ${itemOptions.color === color ? 'selected' : ''} non-clickable`,\n                      style: {\n                        backgroundColor: color\n                      },\n                      \"data-testid\": `cart-item-attribute-${toKebabCase('Color')}-${toKebabCase(color)}${itemOptions.color === color ? '-selected' : ''}`\n                    }, color, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 240,\n                      columnNumber: 31\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"cart-item-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"cart-item-image\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: item.product.image,\n                    alt: item.product.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 254,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"quantity-section\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"cart-item-controls\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"quantity-btn\",\n                      \"data-testid\": \"cart-item-amount-decrease\",\n                      onClick: () => handleDecreaseQuantity(item.id, item.quantity),\n                      children: \"-\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 258,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"quantity-display\",\n                      children: item.quantity\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 265,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"quantity-btn\",\n                      \"data-testid\": \"cart-item-amount-increase\",\n                      onClick: () => handleIncreaseQuantity(item.id),\n                      children: \"+\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 266,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 257,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"item-number\",\n                    children: index + 1\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this)]\n            }, item.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"total-section\",\n          \"data-testid\": \"cart-total\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Total\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"$\", calculateTotal()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handlePlaceOrder,\n          className: \"place-order-btn\",\n          children: \"Place Order\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 188,\n    columnNumber: 5\n  }, this);\n}\n_s(CartPopup, \"jgI9OXeHumSOMqHY0f4ANNp/d+o=\", false, function () {\n  return [useQuery, useMutation];\n});\n_c = CartPopup;\nexport default CartPopup;\nvar _c;\n$RefreshReg$(_c, \"CartPopup\");", "map": {"version": 3, "names": ["useState", "useQuery", "useMutation", "GET_CART_QUERY", "UPDATE_CART_MUTATION", "jsxDEV", "_jsxDEV", "CartPopup", "isOpen", "closePopup", "cartItems", "_s", "loading", "error", "data", "refetch", "fetchPolicy", "notifyOnNetworkStatusChange", "selectedOptions", "setSelectedOptions", "updateCartItem", "onError", "console", "errorPolicy", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "message", "actualCartItems", "cart", "calculateTotal", "Array", "isArray", "reduce", "total", "item", "product", "price", "quantity", "toFixed", "getTotalItemCount", "toKebabCase", "str", "toLowerCase", "replace", "handleSizeSelect", "itemId", "size", "prev", "handleColorSelect", "color", "getSelectedOptions", "productAttributes", "parsedAttributes", "parseProductAttributes", "availableSizes", "Size", "availableColors", "Color", "attributesString", "JSON", "parse", "getAttributeOptions", "attributes", "attributeName", "handleIncreaseQuantity", "variables", "quantityChange", "refetchQueries", "query", "catch", "alert", "handleDecreaseQuantity", "currentQuantity", "awaitRefetchQueries", "handlePlaceOrder", "_result$data", "_result$data$placeOrd", "response", "fetch", "method", "headers", "body", "stringify", "result", "json", "placeOrder", "success", "_result$data2", "_result$data2$placeOr", "Error", "err", "onClick", "e", "stopPropagation", "length", "map", "index", "itemOptions", "id", "name", "style", "backgroundColor", "src", "image", "alt", "_c", "$RefreshReg$"], "sources": ["D:/SCAAND_PRO/my-ecommerce-frontend/src/components/CartPopup.js"], "sourcesContent": ["import { useState } from 'react';\r\nimport { useQuery, useMutation } from '@apollo/client';\r\nimport { GET_CART_QUERY } from '../graphql/queries';\r\nimport { UPDATE_CART_MUTATION } from '../graphql/mutations';\r\nimport '../styles/CartPopup.css';\r\n\r\nfunction CartPopup({ isOpen, closePopup, cartItems }) {\r\n  const { loading, error, data, refetch } = useQuery(GET_CART_QUERY, {\r\n    fetchPolicy: 'cache-and-network',\r\n    notifyOnNetworkStatusChange: true\r\n  });\r\n\r\n  // State for managing selected options for each cart item\r\n  const [selectedOptions, setSelectedOptions] = useState({});\r\n\r\n\r\n\r\n\r\n  // Mutations\r\n  const [updateCartItem] = useMutation(UPDATE_CART_MUTATION, {\r\n    onError: (error) => {\r\n      console.error('Update cart error:', error);\r\n    },\r\n    errorPolicy: 'all'\r\n  });\r\n\r\n\r\n\r\n  // Prevent rendering if the popup is closed\r\n  if (!isOpen) return null;\r\n\r\n  // Show loading state\r\n  if (loading) return <div className=\"cart-popup\">Loading...</div>;\r\n\r\n  // Show error state\r\n  if (error) return <div className=\"cart-popup\">Error loading cart: {error.message}</div>;\r\n\r\n  // Use data from query instead of props for consistency\r\n  const actualCartItems = data?.cart || cartItems || [];\r\n\r\n  const calculateTotal = () => {\r\n    if (!actualCartItems || !Array.isArray(actualCartItems)) return '0.00';\r\n    return actualCartItems.reduce((total, item) => total + item.product.price * item.quantity, 0).toFixed(2);\r\n  };\r\n\r\n  const getTotalItemCount = () => {\r\n    if (!actualCartItems || !Array.isArray(actualCartItems)) return 0;\r\n    return actualCartItems.reduce((total, item) => total + item.quantity, 0);\r\n  };\r\n\r\n  // Helper function to convert attribute name to kebab case\r\n  const toKebabCase = (str) => {\r\n    return str.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, '');\r\n  };\r\n\r\n  // Handle size selection\r\n  const handleSizeSelect = (itemId, size) => {\r\n    setSelectedOptions(prev => ({\r\n      ...prev,\r\n      [itemId]: {\r\n        ...prev[itemId],\r\n        size: size\r\n      }\r\n    }));\r\n  };\r\n\r\n  // Handle color selection\r\n  const handleColorSelect = (itemId, color) => {\r\n    setSelectedOptions(prev => ({\r\n      ...prev,\r\n      [itemId]: {\r\n        ...prev[itemId],\r\n        color: color\r\n      }\r\n    }));\r\n  };\r\n\r\n  // Get selected options for an item with defaults\r\n  const getSelectedOptions = (itemId, productAttributes) => {\r\n    if (!selectedOptions[itemId]) {\r\n      // Set default selections based on available attributes\r\n      const parsedAttributes = parseProductAttributes(productAttributes);\r\n      const availableSizes = parsedAttributes.Size || parsedAttributes.size || ['S'];\r\n      const availableColors = parsedAttributes.Color || parsedAttributes.color || ['#2B5D31'];\r\n\r\n      return {\r\n        size: availableSizes[0] || 'S',\r\n        color: availableColors[0] || '#2B5D31'\r\n      };\r\n    }\r\n    return selectedOptions[itemId];\r\n  };\r\n\r\n  // Parse product attributes\r\n  const parseProductAttributes = (attributesString) => {\r\n    try {\r\n      if (!attributesString) return {};\r\n      return JSON.parse(attributesString);\r\n    } catch (error) {\r\n      return {};\r\n    }\r\n  };\r\n\r\n  // Get attribute options for display\r\n  const getAttributeOptions = (attributes, attributeName) => {\r\n    const parsedAttributes = parseProductAttributes(attributes);\r\n    return parsedAttributes[attributeName] || [];\r\n  };\r\n\r\n  // Increase Quantity Handler\r\n  const handleIncreaseQuantity = (itemId) => {\r\n    updateCartItem({\r\n      variables: { itemId, quantityChange: 1 },\r\n      refetchQueries: [{ query: GET_CART_QUERY }],\r\n    })\r\n    .catch((error) => {\r\n      console.error('Error increasing quantity:', error);\r\n      alert('Failed to update quantity. Please try again.');\r\n    });\r\n  };\r\n\r\n  // Decrease Quantity Handler - now removes item when quantity reaches 0\r\n  const handleDecreaseQuantity = (itemId, currentQuantity) => {\r\n    if (currentQuantity > 1) {\r\n      // Decrease quantity by 1\r\n      updateCartItem({\r\n        variables: { itemId, quantityChange: -1 },\r\n        refetchQueries: [{ query: GET_CART_QUERY }],\r\n      })\r\n      .catch((error) => {\r\n        console.error('Error decreasing quantity:', error);\r\n        alert('Failed to update quantity. Please try again.');\r\n      });\r\n    } else {\r\n      // When quantity is 1, decrease it to 0 which should remove the item\r\n      updateCartItem({\r\n        variables: { itemId, quantityChange: -1 },\r\n        refetchQueries: [{ query: GET_CART_QUERY }],\r\n        awaitRefetchQueries: true\r\n      })\r\n      .catch((error) => {\r\n        console.error('Error removing item via quantity decrease:', error);\r\n        alert('Failed to remove item. Please try again.');\r\n      });\r\n    }\r\n  };\r\n\r\n\r\n\r\n  // Place Order Handler using dedicated endpoint\r\n  const handlePlaceOrder = async () => {\r\n    try {\r\n      const response = await fetch('http://localhost:8000/place_order_endpoint.php', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          query: `\r\n            mutation PlaceOrder {\r\n              placeOrder {\r\n                success\r\n                message\r\n              }\r\n            }\r\n          `\r\n        })\r\n      });\r\n\r\n      const result = await response.json();\r\n\r\n      if (result.data?.placeOrder?.success) {\r\n        // Refetch the cart data to update UI\r\n        await refetch();\r\n        alert('Order placed successfully!');\r\n        closePopup(); // Close the cart popup\r\n      } else {\r\n        throw new Error(result.data?.placeOrder?.message || 'Order failed');\r\n      }\r\n\r\n    } catch (err) {\r\n      console.error('Place order error:', err);\r\n      alert(`Failed to place order: ${err.message}`);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"modalBackground\" onClick={closePopup}>\r\n      <div className=\"modalContainer\" onClick={(e) => e.stopPropagation()}>\r\n        <div className=\"cart-header\">\r\n          <h2>My Bag, {getTotalItemCount()} items</h2>\r\n          <button className=\"close-btn\" onClick={closePopup}>×</button>\r\n        </div>\r\n        {!actualCartItems || actualCartItems.length === 0 ? (\r\n          <p>Your cart is empty.</p>\r\n        ) : (\r\n          <div className=\"cart-content\">\r\n            <div className=\"cart-items-container\">\r\n              {actualCartItems?.map((item, index) => {\r\n                const itemOptions = getSelectedOptions(item.id, item.product.attributes);\r\n\r\n                // Get available sizes and colors from product attributes\r\n                const availableSizes = getAttributeOptions(item.product.attributes, 'Size') ||\r\n                                     getAttributeOptions(item.product.attributes, 'size') ||\r\n                                     ['XS', 'S', 'M', 'L']; // fallback\r\n                const availableColors = getAttributeOptions(item.product.attributes, 'Color') ||\r\n                                       getAttributeOptions(item.product.attributes, 'color') ||\r\n                                       ['#C4D79B', '#2B5D31', '#0F4C3A']; // fallback\r\n\r\n\r\n\r\n                return (\r\n                  <div key={item.id} className=\"cart-item\">\r\n                    <div className=\"cart-item-left\">\r\n                      <h3 className=\"product-name\">{item.product.name}</h3>\r\n                      <p className=\"product-price\" data-testid='cart-item-amount'>${item.product.price}</p>\r\n\r\n                      {availableSizes.length > 0 && (\r\n                        <div className=\"size-section\" data-testid={`cart-item-attribute-${toKebabCase('Size')}`}>\r\n                          <span className=\"size-label\">Size:</span>\r\n                          <div className=\"size-options\">\r\n                            {availableSizes.map(size => (\r\n                              <span\r\n                                key={size}\r\n                                className={`size-btn ${itemOptions.size === size ? 'selected' : ''} non-clickable`}\r\n                                data-testid={`cart-item-attribute-${toKebabCase('Size')}-${toKebabCase(size)}${itemOptions.size === size ? '-selected' : ''}`}\r\n                              >\r\n                                {size}\r\n                              </span>\r\n                            ))}\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {availableColors.length > 0 && (\r\n                        <div className=\"color-section\" data-testid={`cart-item-attribute-${toKebabCase('Color')}`}>\r\n                          <span className=\"color-label\">Color:</span>\r\n                          <div className=\"color-options\">\r\n                            {availableColors.map(color => (\r\n                              <div\r\n                                key={color}\r\n                                className={`color-circle ${itemOptions.color === color ? 'selected' : ''} non-clickable`}\r\n                                style={{backgroundColor: color}}\r\n                                data-testid={`cart-item-attribute-${toKebabCase('Color')}-${toKebabCase(color)}${itemOptions.color === color ? '-selected' : ''}`}\r\n                              ></div>\r\n                            ))}\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n\r\n                  <div className=\"cart-item-right\">\r\n                    <div className=\"cart-item-image\">\r\n                      <img src={item.product.image} alt={item.product.name} />\r\n                    </div>\r\n                    <div className=\"quantity-section\">\r\n                      <div className=\"cart-item-controls\">\r\n                        <button\r\n                          className=\"quantity-btn\"\r\n                          data-testid='cart-item-amount-decrease'\r\n                          onClick={() => handleDecreaseQuantity(item.id, item.quantity)}\r\n                        >\r\n                          -\r\n                        </button>\r\n                        <span className=\"quantity-display\">{item.quantity}</span>\r\n                        <button\r\n                          className=\"quantity-btn\"\r\n                          data-testid='cart-item-amount-increase'\r\n                          onClick={() => handleIncreaseQuantity(item.id)}\r\n                        >\r\n                          +\r\n                        </button>\r\n                      </div>\r\n                      <div className=\"item-number\">{index + 1}</div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                );\r\n              })}\r\n            </div>\r\n            {/* Total Section */}\r\n            <div className=\"total-section\" data-testid=\"cart-total\">\r\n              <p>Total</p>\r\n              <p>${calculateTotal()}</p>\r\n            </div>\r\n            <button onClick={handlePlaceOrder} className=\"place-order-btn\">\r\n              Place Order\r\n            </button>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default CartPopup;\r\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,EAAEC,WAAW,QAAQ,gBAAgB;AACtD,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SAASC,oBAAoB,QAAQ,sBAAsB;AAC3D,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,SAASC,SAASA,CAAC;EAAEC,MAAM;EAAEC,UAAU;EAAEC;AAAU,CAAC,EAAE;EAAAC,EAAA;EACpD,MAAM;IAAEC,OAAO;IAAEC,KAAK;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGd,QAAQ,CAACE,cAAc,EAAE;IACjEa,WAAW,EAAE,mBAAmB;IAChCC,2BAA2B,EAAE;EAC/B,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAK1D;EACA,MAAM,CAACoB,cAAc,CAAC,GAAGlB,WAAW,CAACE,oBAAoB,EAAE;IACzDiB,OAAO,EAAGR,KAAK,IAAK;MAClBS,OAAO,CAACT,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;IAC5C,CAAC;IACDU,WAAW,EAAE;EACf,CAAC,CAAC;;EAIF;EACA,IAAI,CAACf,MAAM,EAAE,OAAO,IAAI;;EAExB;EACA,IAAII,OAAO,EAAE,oBAAON,OAAA;IAAKkB,SAAS,EAAC,YAAY;IAAAC,QAAA,EAAC;EAAU;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;;EAEhE;EACA,IAAIhB,KAAK,EAAE,oBAAOP,OAAA;IAAKkB,SAAS,EAAC,YAAY;IAAAC,QAAA,GAAC,sBAAoB,EAACZ,KAAK,CAACiB,OAAO;EAAA;IAAAJ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;;EAEvF;EACA,MAAME,eAAe,GAAG,CAAAjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,IAAI,KAAItB,SAAS,IAAI,EAAE;EAErD,MAAMuB,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAACF,eAAe,IAAI,CAACG,KAAK,CAACC,OAAO,CAACJ,eAAe,CAAC,EAAE,OAAO,MAAM;IACtE,OAAOA,eAAe,CAACK,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAKD,KAAK,GAAGC,IAAI,CAACC,OAAO,CAACC,KAAK,GAAGF,IAAI,CAACG,QAAQ,EAAE,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;EAC1G,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAACZ,eAAe,IAAI,CAACG,KAAK,CAACC,OAAO,CAACJ,eAAe,CAAC,EAAE,OAAO,CAAC;IACjE,OAAOA,eAAe,CAACK,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAKD,KAAK,GAAGC,IAAI,CAACG,QAAQ,EAAE,CAAC,CAAC;EAC1E,CAAC;;EAED;EACA,MAAMG,WAAW,GAAIC,GAAG,IAAK;IAC3B,OAAOA,GAAG,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;EAC1E,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAACC,MAAM,EAAEC,IAAI,KAAK;IACzC/B,kBAAkB,CAACgC,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACP,CAACF,MAAM,GAAG;QACR,GAAGE,IAAI,CAACF,MAAM,CAAC;QACfC,IAAI,EAAEA;MACR;IACF,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,iBAAiB,GAAGA,CAACH,MAAM,EAAEI,KAAK,KAAK;IAC3ClC,kBAAkB,CAACgC,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACP,CAACF,MAAM,GAAG;QACR,GAAGE,IAAI,CAACF,MAAM,CAAC;QACfI,KAAK,EAAEA;MACT;IACF,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAACL,MAAM,EAAEM,iBAAiB,KAAK;IACxD,IAAI,CAACrC,eAAe,CAAC+B,MAAM,CAAC,EAAE;MAC5B;MACA,MAAMO,gBAAgB,GAAGC,sBAAsB,CAACF,iBAAiB,CAAC;MAClE,MAAMG,cAAc,GAAGF,gBAAgB,CAACG,IAAI,IAAIH,gBAAgB,CAACN,IAAI,IAAI,CAAC,GAAG,CAAC;MAC9E,MAAMU,eAAe,GAAGJ,gBAAgB,CAACK,KAAK,IAAIL,gBAAgB,CAACH,KAAK,IAAI,CAAC,SAAS,CAAC;MAEvF,OAAO;QACLH,IAAI,EAAEQ,cAAc,CAAC,CAAC,CAAC,IAAI,GAAG;QAC9BL,KAAK,EAAEO,eAAe,CAAC,CAAC,CAAC,IAAI;MAC/B,CAAC;IACH;IACA,OAAO1C,eAAe,CAAC+B,MAAM,CAAC;EAChC,CAAC;;EAED;EACA,MAAMQ,sBAAsB,GAAIK,gBAAgB,IAAK;IACnD,IAAI;MACF,IAAI,CAACA,gBAAgB,EAAE,OAAO,CAAC,CAAC;MAChC,OAAOC,IAAI,CAACC,KAAK,CAACF,gBAAgB,CAAC;IACrC,CAAC,CAAC,OAAOjD,KAAK,EAAE;MACd,OAAO,CAAC,CAAC;IACX;EACF,CAAC;;EAED;EACA,MAAMoD,mBAAmB,GAAGA,CAACC,UAAU,EAAEC,aAAa,KAAK;IACzD,MAAMX,gBAAgB,GAAGC,sBAAsB,CAACS,UAAU,CAAC;IAC3D,OAAOV,gBAAgB,CAACW,aAAa,CAAC,IAAI,EAAE;EAC9C,CAAC;;EAED;EACA,MAAMC,sBAAsB,GAAInB,MAAM,IAAK;IACzC7B,cAAc,CAAC;MACbiD,SAAS,EAAE;QAAEpB,MAAM;QAAEqB,cAAc,EAAE;MAAE,CAAC;MACxCC,cAAc,EAAE,CAAC;QAAEC,KAAK,EAAErE;MAAe,CAAC;IAC5C,CAAC,CAAC,CACDsE,KAAK,CAAE5D,KAAK,IAAK;MAChBS,OAAO,CAACT,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD6D,KAAK,CAAC,8CAA8C,CAAC;IACvD,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,sBAAsB,GAAGA,CAAC1B,MAAM,EAAE2B,eAAe,KAAK;IAC1D,IAAIA,eAAe,GAAG,CAAC,EAAE;MACvB;MACAxD,cAAc,CAAC;QACbiD,SAAS,EAAE;UAAEpB,MAAM;UAAEqB,cAAc,EAAE,CAAC;QAAE,CAAC;QACzCC,cAAc,EAAE,CAAC;UAAEC,KAAK,EAAErE;QAAe,CAAC;MAC5C,CAAC,CAAC,CACDsE,KAAK,CAAE5D,KAAK,IAAK;QAChBS,OAAO,CAACT,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD6D,KAAK,CAAC,8CAA8C,CAAC;MACvD,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAtD,cAAc,CAAC;QACbiD,SAAS,EAAE;UAAEpB,MAAM;UAAEqB,cAAc,EAAE,CAAC;QAAE,CAAC;QACzCC,cAAc,EAAE,CAAC;UAAEC,KAAK,EAAErE;QAAe,CAAC,CAAC;QAC3C0E,mBAAmB,EAAE;MACvB,CAAC,CAAC,CACDJ,KAAK,CAAE5D,KAAK,IAAK;QAChBS,OAAO,CAACT,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QAClE6D,KAAK,CAAC,0CAA0C,CAAC;MACnD,CAAC,CAAC;IACJ;EACF,CAAC;;EAID;EACA,MAAMI,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MAAA,IAAAC,YAAA,EAAAC,qBAAA;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,gDAAgD,EAAE;QAC7EC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEtB,IAAI,CAACuB,SAAS,CAAC;UACnBd,KAAK,EAAE;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;QACQ,CAAC;MACH,CAAC,CAAC;MAEF,MAAMe,MAAM,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;MAEpC,KAAAT,YAAA,GAAIQ,MAAM,CAACzE,IAAI,cAAAiE,YAAA,gBAAAC,qBAAA,GAAXD,YAAA,CAAaU,UAAU,cAAAT,qBAAA,eAAvBA,qBAAA,CAAyBU,OAAO,EAAE;QACpC;QACA,MAAM3E,OAAO,CAAC,CAAC;QACf2D,KAAK,CAAC,4BAA4B,CAAC;QACnCjE,UAAU,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,MAAM;QAAA,IAAAkF,aAAA,EAAAC,qBAAA;QACL,MAAM,IAAIC,KAAK,CAAC,EAAAF,aAAA,GAAAJ,MAAM,CAACzE,IAAI,cAAA6E,aAAA,wBAAAC,qBAAA,GAAXD,aAAA,CAAaF,UAAU,cAAAG,qBAAA,uBAAvBA,qBAAA,CAAyB9D,OAAO,KAAI,cAAc,CAAC;MACrE;IAEF,CAAC,CAAC,OAAOgE,GAAG,EAAE;MACZxE,OAAO,CAACT,KAAK,CAAC,oBAAoB,EAAEiF,GAAG,CAAC;MACxCpB,KAAK,CAAC,0BAA0BoB,GAAG,CAAChE,OAAO,EAAE,CAAC;IAChD;EACF,CAAC;EAED,oBACExB,OAAA;IAAKkB,SAAS,EAAC,iBAAiB;IAACuE,OAAO,EAAEtF,UAAW;IAAAgB,QAAA,eACnDnB,OAAA;MAAKkB,SAAS,EAAC,gBAAgB;MAACuE,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;MAAAxE,QAAA,gBAClEnB,OAAA;QAAKkB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BnB,OAAA;UAAAmB,QAAA,GAAI,UAAQ,EAACkB,iBAAiB,CAAC,CAAC,EAAC,QAAM;QAAA;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5CvB,OAAA;UAAQkB,SAAS,EAAC,WAAW;UAACuE,OAAO,EAAEtF,UAAW;UAAAgB,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,EACL,CAACE,eAAe,IAAIA,eAAe,CAACmE,MAAM,KAAK,CAAC,gBAC/C5F,OAAA;QAAAmB,QAAA,EAAG;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,gBAE1BvB,OAAA;QAAKkB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BnB,OAAA;UAAKkB,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAClCM,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEoE,GAAG,CAAC,CAAC7D,IAAI,EAAE8D,KAAK,KAAK;YACrC,MAAMC,WAAW,GAAG/C,kBAAkB,CAAChB,IAAI,CAACgE,EAAE,EAAEhE,IAAI,CAACC,OAAO,CAAC2B,UAAU,CAAC;;YAExE;YACA,MAAMR,cAAc,GAAGO,mBAAmB,CAAC3B,IAAI,CAACC,OAAO,CAAC2B,UAAU,EAAE,MAAM,CAAC,IACtDD,mBAAmB,CAAC3B,IAAI,CAACC,OAAO,CAAC2B,UAAU,EAAE,MAAM,CAAC,IACpD,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;YAC5C,MAAMN,eAAe,GAAGK,mBAAmB,CAAC3B,IAAI,CAACC,OAAO,CAAC2B,UAAU,EAAE,OAAO,CAAC,IACtDD,mBAAmB,CAAC3B,IAAI,CAACC,OAAO,CAAC2B,UAAU,EAAE,OAAO,CAAC,IACrD,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;;YAI1D,oBACE5D,OAAA;cAAmBkB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtCnB,OAAA;gBAAKkB,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BnB,OAAA;kBAAIkB,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEa,IAAI,CAACC,OAAO,CAACgE;gBAAI;kBAAA7E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrDvB,OAAA;kBAAGkB,SAAS,EAAC,eAAe;kBAAC,eAAY,kBAAkB;kBAAAC,QAAA,GAAC,GAAC,EAACa,IAAI,CAACC,OAAO,CAACC,KAAK;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAEpF6B,cAAc,CAACwC,MAAM,GAAG,CAAC,iBACxB5F,OAAA;kBAAKkB,SAAS,EAAC,cAAc;kBAAC,eAAa,uBAAuBoB,WAAW,CAAC,MAAM,CAAC,EAAG;kBAAAnB,QAAA,gBACtFnB,OAAA;oBAAMkB,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzCvB,OAAA;oBAAKkB,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAC1BiC,cAAc,CAACyC,GAAG,CAACjD,IAAI,iBACtB5C,OAAA;sBAEEkB,SAAS,EAAE,YAAY6E,WAAW,CAACnD,IAAI,KAAKA,IAAI,GAAG,UAAU,GAAG,EAAE,gBAAiB;sBACnF,eAAa,uBAAuBN,WAAW,CAAC,MAAM,CAAC,IAAIA,WAAW,CAACM,IAAI,CAAC,GAAGmD,WAAW,CAACnD,IAAI,KAAKA,IAAI,GAAG,WAAW,GAAG,EAAE,EAAG;sBAAAzB,QAAA,EAE7HyB;oBAAI,GAJAA,IAAI;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAKL,CACP;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,EAEA+B,eAAe,CAACsC,MAAM,GAAG,CAAC,iBACzB5F,OAAA;kBAAKkB,SAAS,EAAC,eAAe;kBAAC,eAAa,uBAAuBoB,WAAW,CAAC,OAAO,CAAC,EAAG;kBAAAnB,QAAA,gBACxFnB,OAAA;oBAAMkB,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3CvB,OAAA;oBAAKkB,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAC3BmC,eAAe,CAACuC,GAAG,CAAC9C,KAAK,iBACxB/C,OAAA;sBAEEkB,SAAS,EAAE,gBAAgB6E,WAAW,CAAChD,KAAK,KAAKA,KAAK,GAAG,UAAU,GAAG,EAAE,gBAAiB;sBACzFmD,KAAK,EAAE;wBAACC,eAAe,EAAEpD;sBAAK,CAAE;sBAChC,eAAa,uBAAuBT,WAAW,CAAC,OAAO,CAAC,IAAIA,WAAW,CAACS,KAAK,CAAC,GAAGgD,WAAW,CAAChD,KAAK,KAAKA,KAAK,GAAG,WAAW,GAAG,EAAE;oBAAG,GAH7HA,KAAK;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAIN,CACP;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAERvB,OAAA;gBAAKkB,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BnB,OAAA;kBAAKkB,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,eAC9BnB,OAAA;oBAAKoG,GAAG,EAAEpE,IAAI,CAACC,OAAO,CAACoE,KAAM;oBAACC,GAAG,EAAEtE,IAAI,CAACC,OAAO,CAACgE;kBAAK;oBAAA7E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACNvB,OAAA;kBAAKkB,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/BnB,OAAA;oBAAKkB,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,gBACjCnB,OAAA;sBACEkB,SAAS,EAAC,cAAc;sBACxB,eAAY,2BAA2B;sBACvCuE,OAAO,EAAEA,CAAA,KAAMpB,sBAAsB,CAACrC,IAAI,CAACgE,EAAE,EAAEhE,IAAI,CAACG,QAAQ,CAAE;sBAAAhB,QAAA,EAC/D;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTvB,OAAA;sBAAMkB,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEa,IAAI,CAACG;oBAAQ;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACzDvB,OAAA;sBACEkB,SAAS,EAAC,cAAc;sBACxB,eAAY,2BAA2B;sBACvCuE,OAAO,EAAEA,CAAA,KAAM3B,sBAAsB,CAAC9B,IAAI,CAACgE,EAAE,CAAE;sBAAA7E,QAAA,EAChD;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACNvB,OAAA;oBAAKkB,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAE2E,KAAK,GAAG;kBAAC;oBAAA1E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GA/DIS,IAAI,CAACgE,EAAE;cAAA5E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgEd,CAAC;UAER,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENvB,OAAA;UAAKkB,SAAS,EAAC,eAAe;UAAC,eAAY,YAAY;UAAAC,QAAA,gBACrDnB,OAAA;YAAAmB,QAAA,EAAG;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACZvB,OAAA;YAAAmB,QAAA,GAAG,GAAC,EAACQ,cAAc,CAAC,CAAC;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACNvB,OAAA;UAAQyF,OAAO,EAAEjB,gBAAiB;UAACtD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAE/D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAClB,EAAA,CA/RQJ,SAAS;EAAA,QAC0BN,QAAQ,EAYzBC,WAAW;AAAA;AAAA2G,EAAA,GAb7BtG,SAAS;AAiSlB,eAAeA,SAAS;AAAC,IAAAsG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}